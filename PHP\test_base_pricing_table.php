<?php
require_once 'config.php';

header('Content-Type: application/json; charset=utf-8');

$local_link = get_db_connection();

if (!$local_link) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

// Check if table exists
$sql = "SHOW TABLES LIKE 'pc_base_pricing'";
$result = mysqli_query($local_link, $sql);

if (mysqli_num_rows($result) == 0) {
    echo json_encode(['success' => false, 'message' => 'Table pc_base_pricing does not exist']);
    close_db_connection($local_link);
    exit;
}

// Check table structure
$sql = "DESCRIBE pc_base_pricing";
$result = mysqli_query($local_link, $sql);

$columns = [];
while ($row = mysqli_fetch_assoc($result)) {
    $columns[] = $row;
}

// Check if data exists
$sql = "SELECT COUNT(*) as count FROM pc_base_pricing";
$result = mysqli_query($local_link, $sql);
$count_row = mysqli_fetch_assoc($result);

close_db_connection($local_link);

echo json_encode([
    'success' => true,
    'table_exists' => true,
    'columns' => $columns,
    'row_count' => $count_row['count']
]);
?>
