<?php
require_once 'config.php';

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $description = $_POST['description'] ?? '';
    
    echo json_encode([
        'success' => true,
        'received_description' => $description,
        'description_length' => strlen($description),
        'description_mb_length' => mb_strlen($description),
        'is_utf8' => mb_check_encoding($description, 'UTF-8'),
        'raw_post' => $_POST
    ], JSON_UNESCAPED_UNICODE);
} else {
    echo json_encode(['success' => false, 'message' => 'Only POST method allowed']);
}
?>
