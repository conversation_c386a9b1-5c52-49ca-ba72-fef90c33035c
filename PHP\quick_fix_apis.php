<?php
require_once 'config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<h1>Quick API Fix</h1>";

// Step 1: Setup all PC tables
echo "<h2>Step 1: Setting up PC tables...</h2>";
$local_link = get_db_connection();

if (!$local_link) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    exit;
}

// Create all necessary tables
$tables_sql = [
    'pc_component_categories' => "CREATE TABLE IF NOT EXISTS pc_component_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category_name VARCHAR(100) NOT NULL,
        category_name_en VARCHAR(100) NOT NULL,
        category_name_zh VARCHAR(100) NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order)
    )",
    
    'pc_components' => "CREATE TABLE IF NOT EXISTS pc_components (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category_id INT NOT NULL,
        component_name VARCHAR(200) NOT NULL,
        component_name_en VARCHAR(200) NOT NULL,
        component_name_zh VARCHAR(200) NOT NULL,
        brand VARCHAR(100),
        model VARCHAR(100),
        specifications JSON,
        base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        stock_quantity INT DEFAULT 0,
        description TEXT,
        description_en TEXT,
        description_zh TEXT,
        image_url VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_category (category_id),
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order),
        INDEX idx_price (current_price)
    )",
    
    'pc_prebuilt_configs' => "CREATE TABLE IF NOT EXISTS pc_prebuilt_configs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_name VARCHAR(200) NOT NULL,
        config_name_en VARCHAR(200) NOT NULL,
        config_name_zh VARCHAR(200) NOT NULL,
        tier ENUM('budget', 'mid', 'high', 'premium') DEFAULT 'mid',
        primary_use ENUM('gaming', 'work', 'content_creation', 'general') DEFAULT 'general',
        description TEXT,
        description_en TEXT,
        description_zh TEXT,
        components JSON NOT NULL,
        specifications_summary JSON,
        base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        image_url VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order),
        INDEX idx_tier (tier),
        INDEX idx_use (primary_use),
        INDEX idx_price (current_price)
    )",
    
    'pc_orders' => "CREATE TABLE IF NOT EXISTS pc_orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        order_type ENUM('custom', 'prebuilt') NOT NULL DEFAULT 'custom',
        config_id INT NULL,
        components JSON,
        total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
        payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
        payment_method VARCHAR(50),
        shipping_address TEXT,
        notes TEXT,
        admin_notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user (user_id),
        INDEX idx_status (status),
        INDEX idx_payment_status (payment_status),
        INDEX idx_created (created_at)
    )",
    
    'pc_base_pricing' => "CREATE TABLE IF NOT EXISTS pc_base_pricing (
        id INT AUTO_INCREMENT PRIMARY KEY,
        pricing_type ENUM('base_price', 'case_price') NOT NULL,
        config_key VARCHAR(50) NOT NULL,
        price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        description TEXT,
        description_en TEXT,
        description_zh TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_pricing (pricing_type, config_key),
        INDEX idx_type (pricing_type),
        INDEX idx_active (is_active)
    )"
];

foreach ($tables_sql as $table_name => $sql) {
    if (mysqli_query($local_link, $sql)) {
        echo "<p style='color: green;'>✅ Table '$table_name' created/verified</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create '$table_name': " . mysqli_error($local_link) . "</p>";
    }
}

// Insert default categories
echo "<h3>Inserting default categories...</h3>";
$categories = [
    ['CPU', 'CPU', 'CPU', 'Central Processing Unit', 1],
    ['GPU', 'GPU', 'GPU', 'Graphics Processing Unit', 2],
    ['RAM', 'RAM', 'RAM', 'Random Access Memory', 3],
    ['Storage', 'Storage', 'Storage', 'Storage Devices', 4],
    ['PSU', 'PSU', 'PSU', 'Power Supply Unit', 5],
    ['Motherboard', 'Motherboard', 'Motherboard', 'Motherboard', 6]
];

foreach ($categories as $category) {
    $sql = "INSERT IGNORE INTO pc_component_categories (category_name, category_name_en, category_name_zh, description, sort_order) 
            VALUES (?, ?, ?, ?, ?)";
    $stmt = mysqli_prepare($local_link, $sql);
    mysqli_stmt_bind_param($stmt, "ssssi", $category[0], $category[1], $category[2], $category[3], $category[4]);
    if (mysqli_stmt_execute($stmt)) {
        echo "<p style='color: green;'>✅ Category '{$category[0]}' inserted</p>";
    }
    mysqli_stmt_close($stmt);
}

// Insert default base pricing
echo "<h3>Inserting default base pricing...</h3>";
$pricing_data = [
    ['base_price', 'detailed_mode', 0.00, 'Base configuration includes: 10 premium RGB fans, premium RGB power cables, CPU special cooling treatment, premium CPU liquid cooler, GPU Power Adapter, RGB and Fan controller'],
    ['case_price', 'small', 200.00, 'Small case - Premium quality with excellent aesthetics'],
    ['case_price', 'medium', 200.00, 'Medium case - Premium quality with excellent aesthetics'],
    ['case_price', 'large', 300.00, 'Large case - Premium quality with excellent aesthetics']
];

foreach ($pricing_data as $pricing) {
    $sql = "INSERT INTO pc_base_pricing (pricing_type, config_key, price, description, description_en, description_zh, is_active) 
            VALUES (?, ?, ?, ?, ?, ?, 1)
            ON DUPLICATE KEY UPDATE 
            price = VALUES(price),
            description = VALUES(description),
            description_en = VALUES(description_en),
            description_zh = VALUES(description_zh)";
    $stmt = mysqli_prepare($local_link, $sql);
    mysqli_stmt_bind_param($stmt, "ssdsss", $pricing[0], $pricing[1], $pricing[2], $pricing[3], $pricing[3], $pricing[3]);
    if (mysqli_stmt_execute($stmt)) {
        echo "<p style='color: green;'>✅ Pricing '{$pricing[0]} - {$pricing[1]}' inserted</p>";
    }
    mysqli_stmt_close($stmt);
}

close_db_connection($local_link);

echo "<h2>✅ Setup Complete!</h2>";
echo "<p><a href='admin_pc_management.php' target='_blank'>Go to Admin PC Management</a></p>";
echo "<p><a href='test_api_endpoints.php' target='_blank'>Test API Endpoints</a></p>";
echo "<p><a href='debug_database.php' target='_blank'>Debug Database</a></p>";
?>
